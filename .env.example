# LLM Configuration
LLM_TYPE=ollama  # "ollama" or "openai"

# Ollama Configuration (default)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2  # or llama3.1, codellama, mistral, etc.

# OpenAI Configuration (alternative)
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_MODEL=gpt-3.5-turbo

# Database Configuration
# For SQLite (default for testing)
DATABASE_URL=sqlite:///./sample_database.db

# For PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# For MySQL
# DATABASE_URL=mysql+pymysql://username:password@localhost:3306/database_name

# Agent Configuration
MAX_QUERY_TIMEOUT=60  # Increased for local models
MAX_RESULT_ROWS=100
ENABLE_QUERY_LOGGING=true
LOG_LEVEL=INFO

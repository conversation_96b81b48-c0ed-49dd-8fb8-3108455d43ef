# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
# For SQLite (default for testing)
DATABASE_URL=sqlite:///./sample_database.db

# For PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# For MySQL
# DATABASE_URL=mysql+pymysql://username:password@localhost:3306/database_name

# Agent Configuration
MAX_QUERY_TIMEOUT=30
MAX_RESULT_ROWS=100
ENABLE_QUERY_LOGGING=true
LOG_LEVEL=INFO

#!/usr/bin/env python3
"""Test script to verify Ollama setup and basic functionality."""

import sys
import logging
import requests
from config import config
from db import db_manager
from agent import sql_agent

def test_ollama_connection():
    """Test Ollama connection."""
    print("🦙 Testing Ollama connection...")
    
    try:
        # Test if Ollama is running
        response = requests.get(f"{config.OLLAMA_BASE_URL}/api/version", timeout=5)
        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ Ollama is running (version: {version_info.get('version', 'unknown')})")
        else:
            print("❌ Ollama is not responding correctly")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Ollama. Make sure it's running with: ollama serve")
        return False
    except Exception as e:
        print(f"❌ Error connecting to Ollama: {str(e)}")
        return False
    
    # Test if model is available
    try:
        response = requests.get(f"{config.OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            
            if config.OLLAMA_MODEL in model_names:
                print(f"✅ Model {config.OLLAMA_MODEL} is available")
            else:
                print(f"❌ Model {config.OLLAMA_MODEL} is not available")
                print(f"Available models: {', '.join(model_names) if model_names else 'None'}")
                print(f"💡 Try running: ollama pull {config.OLLAMA_MODEL}")
                return False
        else:
            print("❌ Cannot get model list from Ollama")
            return False
    except Exception as e:
        print(f"❌ Error getting model list: {str(e)}")
        return False
    
    return True

def test_agent_initialization():
    """Test SQL agent initialization."""
    print("\n🤖 Testing SQL Agent initialization...")
    
    # Setup logging
    config.setup_logging()
    
    # Test database connection
    if not db_manager.connect():
        print("❌ Failed to connect to database")
        return False
    
    print("✅ Database connection successful")
    
    # Test agent initialization
    if not sql_agent.initialize():
        print("❌ Failed to initialize SQL Agent")
        return False
    
    print("✅ SQL Agent initialized successfully")
    return True

def test_simple_query():
    """Test a simple query."""
    print("\n🔍 Testing a simple query...")
    
    try:
        # Test with a very simple question
        result = sql_agent.query("How many customers are there?")
        
        if result["success"]:
            print("✅ Query executed successfully!")
            print(f"Response: {result['response']}")
            if result.get('sql_query'):
                print(f"Generated SQL: {result['sql_query']}")
        else:
            print(f"❌ Query failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Query test failed: {str(e)}")
        return False
    
    return True

def main():
    """Main test function."""
    print("🧪 Testing Ollama SQL Agent Setup\n")
    
    # Test configuration
    print(f"📋 Configuration:")
    print(f"   LLM Type: {config.LLM_TYPE}")
    print(f"   Ollama URL: {config.OLLAMA_BASE_URL}")
    print(f"   Ollama Model: {config.OLLAMA_MODEL}")
    print(f"   Database URL: {config.DATABASE_URL}")
    print()
    
    # Validate configuration
    if not config.validate():
        print("❌ Configuration validation failed")
        return False
    
    # Test Ollama connection
    if not test_ollama_connection():
        return False
    
    # Test agent initialization
    if not test_agent_initialization():
        return False
    
    # Test simple query
    if not test_simple_query():
        return False
    
    # Cleanup
    db_manager.close()
    
    print("\n🎉 All tests passed!")
    print("Your Ollama SQL Agent is ready to use!")
    print("\nRun the full agent with: python main.py")
    
    return True

if __name__ == "__main__":
    if not main():
        print("\n❌ Some tests failed. Please check the setup.")
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure Ollama is running: ollama serve")
        print("2. Make sure the model is available: ollama pull llama3.2")
        print("3. Check your .env configuration")
        print("4. Run the setup script: python setup_ollama.py")
        sys.exit(1)

#!/usr/bin/env python3
"""Setup script to help users install and configure Ollama."""

import subprocess
import sys
import time
import requests
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def check_ollama_installed():
    """Check if Ollama is installed."""
    try:
        result = subprocess.run(['ollama', '--version'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def check_ollama_running():
    """Check if Ollama service is running."""
    try:
        response = requests.get('http://localhost:11434/api/version', timeout=5)
        return response.status_code == 200
    except:
        return False

def check_model_available(model_name="llama3.2"):
    """Check if a specific model is available."""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        return model_name in result.stdout
    except:
        return False

def install_ollama():
    """Provide instructions for installing Ollama."""
    console.print(Panel(
        """
🦙 **Ollama Installation Instructions**

**macOS/Linux:**
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

**Windows:**
Download from: https://ollama.ai/download

**Alternative (using package managers):**
- **macOS**: `brew install ollama`
- **Linux**: Check your distribution's package manager

After installation, restart your terminal and run this script again.
        """,
        title="📦 Install Ollama",
        border_style="blue"
    ))

def start_ollama():
    """Start Ollama service."""
    console.print("🚀 Starting Ollama service...")
    try:
        # Try to start Ollama in the background
        subprocess.Popen(['ollama', 'serve'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # Wait for service to start
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Waiting for Ollama to start...", total=None)
            
            for _ in range(30):  # Wait up to 30 seconds
                if check_ollama_running():
                    progress.update(task, description="✅ Ollama is running!")
                    return True
                time.sleep(1)
        
        console.print("⚠️  Ollama may be starting in the background. Please wait a moment and try again.")
        return False
        
    except Exception as e:
        console.print(f"❌ Failed to start Ollama: {str(e)}")
        return False

def pull_model(model_name="llama3.2"):
    """Pull a model from Ollama."""
    console.print(f"📥 Pulling model: {model_name}")
    console.print("⏳ This may take several minutes depending on your internet connection...")
    
    try:
        result = subprocess.run(['ollama', 'pull', model_name], check=True)
        console.print(f"✅ Successfully pulled {model_name}")
        return True
    except subprocess.CalledProcessError as e:
        console.print(f"❌ Failed to pull {model_name}: {str(e)}")
        return False

def list_available_models():
    """List available models."""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, check=True)
        console.print("📋 Available models:")
        console.print(result.stdout)
    except subprocess.CalledProcessError:
        console.print("❌ Failed to list models")

def test_ollama_connection():
    """Test Ollama connection with a simple query."""
    console.print("🧪 Testing Ollama connection...")
    try:
        result = subprocess.run([
            'ollama', 'run', 'llama3.2', 
            'Hello! Please respond with just "OK" to confirm you are working.'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and "OK" in result.stdout.upper():
            console.print("✅ Ollama is working correctly!")
            return True
        else:
            console.print("⚠️  Ollama responded but may not be working optimally")
            console.print(f"Response: {result.stdout}")
            return False
            
    except subprocess.TimeoutExpired:
        console.print("⚠️  Ollama test timed out - this is normal for first run")
        return True
    except Exception as e:
        console.print(f"❌ Ollama test failed: {str(e)}")
        return False

def main():
    """Main setup function."""
    console.print(Panel(
        "🦙 **Ollama Setup for LangChain SQL Agent**\n\n"
        "This script will help you set up Ollama to run local LLMs.",
        title="🚀 Setup",
        border_style="green"
    ))
    
    # Check if Ollama is installed
    if not check_ollama_installed():
        console.print("❌ Ollama is not installed")
        install_ollama()
        return
    
    console.print("✅ Ollama is installed")
    
    # Check if Ollama is running
    if not check_ollama_running():
        console.print("⚠️  Ollama service is not running")
        if not start_ollama():
            console.print("Please start Ollama manually by running: ollama serve")
            return
    else:
        console.print("✅ Ollama service is running")
    
    # Check if model is available
    model_name = "llama3.2"
    if not check_model_available(model_name):
        console.print(f"⚠️  Model {model_name} is not available")
        if console.input(f"Would you like to download {model_name}? (y/n): ").lower().startswith('y'):
            if not pull_model(model_name):
                return
    else:
        console.print(f"✅ Model {model_name} is available")
    
    # List available models
    list_available_models()
    
    # Test connection
    test_ollama_connection()
    
    console.print(Panel(
        """
🎉 **Setup Complete!**

You can now run the SQL Agent with:
```bash
python main.py
```

**Available models:** Run `ollama list` to see installed models
**Change model:** Update OLLAMA_MODEL in your .env file
**Popular models:** llama3.2, llama3.1, codellama, mistral, phi3
        """,
        title="✅ Ready to Go!",
        border_style="green"
    ))

if __name__ == "__main__":
    main()

"""SQL Agent creation and management using Lang<PERSON>hain."""

import logging
from typing import Optional, Dict, Any
from langchain_openai import Chat<PERSON>penAI
from langchain_community.agent_toolkits import SQLDatabaseToolkit
from langchain.agents import create_sql_agent
from langchain.agents.agent_types import AgentType
from langchain.schema import AgentAction, AgentFinish
from db import db_manager
from config import config

logger = logging.getLogger(__name__)

class SQLAgent:
    """LangChain SQL Agent for natural language database queries."""
    
    def __init__(self):
        """Initialize the SQL Agent."""
        self.llm = None
        self.toolkit = None
        self.agent = None
        self.is_initialized = False
    
    def initialize(self) -> bool:
        """Initialize the SQL Agent with LLM and database connection.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Initialize the LLM
            self.llm = ChatOpenAI(
                model="gpt-3.5-turbo",
                temperature=0,
                openai_api_key=config.OPENAI_API_KEY,
                request_timeout=config.MAX_QUERY_TIMEOUT
            )
            
            # Get the database connection
            sql_database = db_manager.get_sql_database()
            if not sql_database:
                logger.error("❌ Database connection not available")
                return False
            
            # Create the SQL Database Toolkit
            self.toolkit = SQLDatabaseToolkit(
                db=sql_database,
                llm=self.llm
            )
            
            # Create the SQL Agent
            self.agent = create_sql_agent(
                llm=self.llm,
                toolkit=self.toolkit,
                verbose=config.ENABLE_QUERY_LOGGING,
                agent_type=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
                max_iterations=5,
                max_execution_time=config.MAX_QUERY_TIMEOUT,
                early_stopping_method="generate"
            )
            
            self.is_initialized = True
            logger.info("✅ SQL Agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize SQL Agent: {str(e)}")
            return False
    
    def query(self, question: str) -> Dict[str, Any]:
        """Process a natural language query using the SQL Agent.
        
        Args:
            question: Natural language question about the database
            
        Returns:
            Dictionary containing the response, SQL query (if available), and metadata
        """
        if not self.is_initialized:
            return {
                "success": False,
                "error": "Agent not initialized. Call initialize() first.",
                "response": None,
                "sql_query": None
            }
        
        if not question.strip():
            return {
                "success": False,
                "error": "Empty question provided.",
                "response": None,
                "sql_query": None
            }
        
        try:
            logger.info(f"Processing query: {question}")
            
            # Execute the query through the agent
            result = self.agent.invoke({"input": question})
            
            # Extract the response
            response = result.get("output", "No response generated")
            
            # Try to extract SQL query from the agent's intermediate steps
            sql_query = self._extract_sql_query(result)
            
            logger.info("✅ Query processed successfully")
            
            return {
                "success": True,
                "error": None,
                "response": response,
                "sql_query": sql_query,
                "question": question
            }
            
        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            logger.error(f"❌ {error_msg}")
            
            return {
                "success": False,
                "error": error_msg,
                "response": None,
                "sql_query": None,
                "question": question
            }
    
    def _extract_sql_query(self, result: Dict[str, Any]) -> Optional[str]:
        """Extract SQL query from agent result.
        
        Args:
            result: Agent execution result
            
        Returns:
            SQL query string if found, None otherwise
        """
        try:
            # Look for intermediate steps that might contain SQL
            intermediate_steps = result.get("intermediate_steps", [])
            
            for step in intermediate_steps:
                if isinstance(step, tuple) and len(step) >= 2:
                    action, observation = step[0], step[1]
                    if isinstance(action, AgentAction):
                        # Check if this is a SQL query action
                        if "sql" in action.tool.lower():
                            return action.tool_input
            
            return None
            
        except Exception as e:
            logger.warning(f"Could not extract SQL query: {str(e)}")
            return None
    
    def get_database_info(self) -> Dict[str, Any]:
        """Get information about the connected database.
        
        Returns:
            Dictionary containing database information
        """
        if not self.is_initialized:
            return {"error": "Agent not initialized"}
        
        try:
            table_names = db_manager.get_table_names()
            table_info = db_manager.get_table_info()
            
            return {
                "success": True,
                "table_names": table_names,
                "table_count": len(table_names),
                "schema_info": table_info
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Error getting database info: {str(e)}"
            }
    
    def test_connection(self) -> bool:
        """Test if the agent and database connection are working.
        
        Returns:
            bool: True if test successful, False otherwise
        """
        if not self.is_initialized:
            return False
        
        try:
            # Simple test query
            test_result = self.query("What tables are available in this database?")
            return test_result["success"]
            
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return False

# Global SQL agent instance
sql_agent = SQLAgent()

# 🤖 <PERSON><PERSON><PERSON>n SQL Agent

A powerful AI agent that allows you to query your database using natural language, built with LangChain and OpenAI.

## ✨ Features

- 🗣️ **Natural Language Queries**: Ask questions about your database in plain English
- 🔍 **Automatic SQL Generation**: The AI generates and executes SQL queries automatically
- 📊 **Rich Formatted Results**: Beautiful command-line interface with formatted output
- 🛡️ **Safe Database Access**: Read-only access with query limits and timeouts
- 📝 **Query Logging**: Optional logging of generated SQL queries for debugging
- 🔌 **Multiple Database Support**: Works with SQLite, PostgreSQL, and MySQL
- 🧪 **Sample Database**: Includes a sample database for testing and demonstration

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd LangchainAIAgent
```

### 2. Create Virtual Environment

```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Configure Environment

```bash
cp .env.example .env
```

Edit the `.env` file and add your OpenAI API key:

```env
OPENAI_API_KEY=your_openai_api_key_here
DATABASE_URL=sqlite:///./sample_database.db
```

### 5. Create Sample Database (Optional)

```bash
python sample_data.py
```

### 6. Run the Agent

```bash
python main.py
```

## 📋 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | Your OpenAI API key | Required |
| `DATABASE_URL` | Database connection string | `sqlite:///./sample_database.db` |
| `MAX_QUERY_TIMEOUT` | Query timeout in seconds | `30` |
| `MAX_RESULT_ROWS` | Maximum rows returned | `100` |
| `ENABLE_QUERY_LOGGING` | Enable SQL query logging | `true` |
| `LOG_LEVEL` | Logging level | `INFO` |

### Database Connection Examples

**SQLite:**
```env
DATABASE_URL=sqlite:///./my_database.db
```

**PostgreSQL:**
```env
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
```

**MySQL:**
```env
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/database_name
```

## 💬 Usage Examples

### Sample Questions

Once the agent is running, you can ask questions like:

- "How many customers do we have?"
- "What are the top 5 products by price?"
- "Show me all orders from last month"
- "What's the average order value?"
- "List employees in the Sales department"
- "Which customers have placed more than 3 orders?"

### Special Commands

- `help` - Show help information
- `info` - Display database schema information
- `tables` - List all available tables
- `quit` or `exit` - Exit the application

## 🗃️ Sample Database Schema

The included sample database contains the following tables:

- **customers** - Customer information (name, email, location)
- **products** - Product catalog (name, price, category, stock)
- **orders** - Order records (customer, date, total, status)
- **order_items** - Order line items (product, quantity, price)
- **employees** - Employee records (name, department, salary, hire date)

## 🛡️ Security Features

- **Read-only Access**: The agent only performs SELECT queries
- **Query Limits**: Results are limited to prevent large data dumps
- **Timeout Protection**: Queries timeout after a configurable period
- **Input Validation**: Malformed queries are handled gracefully
- **Error Handling**: Comprehensive error handling and logging

## 🔧 Development

### Project Structure

```
LangchainAIAgent/
├── main.py              # Entry point
├── interface.py         # CLI interface
├── agent.py            # SQL Agent logic
├── db.py               # Database connection
├── config.py           # Configuration management
├── sample_data.py      # Sample database creator
├── requirements.txt    # Dependencies
├── .env.example       # Environment template
└── README.md          # This file
```

### Adding New Features

1. **Custom Database Tables**: Modify `sample_data.py` to add your own tables
2. **New Commands**: Add commands in `interface.py`
3. **Enhanced Security**: Modify `agent.py` to add query validation
4. **Web Interface**: Create a web UI using FastAPI or Streamlit

## 🧪 Testing

### Test with Sample Database

```bash
# Create sample database
python sample_data.py

# Run the agent
python main.py

# Try these sample queries:
# - "How many customers are there?"
# - "What's the most expensive product?"
# - "Show me recent orders"
```

### Test with Your Database

1. Update `DATABASE_URL` in your `.env` file
2. Ensure your database user has read-only permissions
3. Run the agent and test with your data

## 🚨 Troubleshooting

### Common Issues

**"OPENAI_API_KEY is required"**
- Make sure you've set your OpenAI API key in the `.env` file

**"Database connection failed"**
- Check your `DATABASE_URL` format
- Ensure the database is accessible
- Verify credentials and permissions

**"Agent initialization failed"**
- Verify your OpenAI API key is valid
- Check your internet connection
- Ensure you have sufficient OpenAI credits

**"No tables found"**
- Make sure your database contains tables
- Check if your user has permission to view tables
- Try running `python sample_data.py` to create test data

### Debug Mode

Run with verbose logging:

```bash
python main.py --verbose
```

## 📚 Dependencies

- **langchain**: LangChain framework for LLM applications
- **langchain-openai**: OpenAI integration for LangChain
- **sqlalchemy**: Database toolkit and ORM
- **openai**: OpenAI API client
- **rich**: Rich text and beautiful formatting
- **click**: Command-line interface creation
- **python-dotenv**: Environment variable management

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

If you encounter any issues or have questions:

1. Check the troubleshooting section above
2. Review the configuration settings
3. Enable debug mode for more detailed logs
4. Create an issue in the repository

---

**Happy querying! 🎉**

"""Configuration management for the LangChain SQL Agent."""

import os
import logging
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for the SQL Agent."""

    # LLM Configuration
    LLM_TYPE: str = os.getenv("LLM_TYPE", "ollama")  # "ollama" or "openai"

    # Ollama Configuration
    OLLAMA_BASE_URL: str = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    OLLAMA_MODEL: str = os.getenv("OLLAMA_MODEL", "llama3.2")

    # OpenAI Configuration (fallback)
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")

    # Database Configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./sample_database.db")

    # Agent Configuration
    MAX_QUERY_TIMEOUT: int = int(os.getenv("MAX_QUERY_TIMEOUT", "60"))  # Increased for local models
    MAX_RESULT_ROWS: int = int(os.getenv("MAX_RESULT_ROWS", "100"))
    ENABLE_QUERY_LOGGING: bool = os.getenv("ENABLE_QUERY_LOGGING", "true").lower() == "true"
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    @classmethod
    def validate(cls) -> bool:
        """Validate that all required configuration is present."""
        if cls.LLM_TYPE.lower() == "openai" and not cls.OPENAI_API_KEY:
            print("❌ Error: OPENAI_API_KEY is required when using OpenAI. Please set it in your .env file.")
            return False

        if not cls.DATABASE_URL:
            print("❌ Error: DATABASE_URL is required. Please set it in your .env file.")
            return False

        return True
    
    @classmethod
    def setup_logging(cls) -> None:
        """Set up logging configuration."""
        logging.basicConfig(
            level=getattr(logging, cls.LOG_LEVEL.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

# Global config instance
config = Config()

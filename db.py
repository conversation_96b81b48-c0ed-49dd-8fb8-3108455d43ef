"""Database connection and LangChain SQLDatabase utility."""

import logging
from typing import Optional, List
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from langchain_community.utilities import SQLDatabase
from config import config

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database connections and LangChain SQLDatabase integration."""
    
    def __init__(self, database_url: Optional[str] = None):
        """Initialize the database manager.
        
        Args:
            database_url: Database connection URL. If None, uses config.DATABASE_URL
        """
        self.database_url = database_url or config.DATABASE_URL
        self.engine = None
        self.sql_database = None
        
    def connect(self) -> bool:
        """Establish database connection.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Create SQLAlchemy engine
            self.engine = create_engine(
                self.database_url,
                pool_timeout=config.MAX_QUERY_TIMEOUT,
                pool_recycle=3600,  # Recycle connections after 1 hour
                echo=config.ENABLE_QUERY_LOGGING
            )
            
            # Test the connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            # Create LangChain SQLDatabase instance
            self.sql_database = SQLDatabase(
                engine=self.engine,
                max_string_length=10000,
                sample_rows_in_table_info=3
            )
            
            logger.info(f"✅ Successfully connected to database: {self._get_db_type()}")
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"❌ Database connection failed: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during database connection: {str(e)}")
            return False
    
    def get_sql_database(self) -> Optional[SQLDatabase]:
        """Get the LangChain SQLDatabase instance.
        
        Returns:
            SQLDatabase instance if connected, None otherwise
        """
        if not self.sql_database:
            logger.warning("Database not connected. Call connect() first.")
        return self.sql_database
    
    def get_table_names(self) -> List[str]:
        """Get list of table names in the database.
        
        Returns:
            List of table names
        """
        if not self.sql_database:
            return []
        
        try:
            return self.sql_database.get_usable_table_names()
        except Exception as e:
            logger.error(f"Error getting table names: {str(e)}")
            return []
    
    def get_table_info(self, table_names: Optional[List[str]] = None) -> str:
        """Get table schema information.
        
        Args:
            table_names: List of specific table names. If None, gets info for all tables.
            
        Returns:
            String containing table schema information
        """
        if not self.sql_database:
            return "Database not connected."
        
        try:
            if table_names:
                return self.sql_database.get_table_info(table_names=table_names)
            else:
                return self.sql_database.get_table_info()
        except Exception as e:
            logger.error(f"Error getting table info: {str(e)}")
            return f"Error retrieving table information: {str(e)}"
    
    def test_query(self, query: str) -> str:
        """Test a SQL query safely.
        
        Args:
            query: SQL query to test
            
        Returns:
            Query result or error message
        """
        if not self.sql_database:
            return "Database not connected."
        
        try:
            # Limit the number of rows returned
            if "LIMIT" not in query.upper():
                query = f"{query.rstrip(';')} LIMIT {config.MAX_RESULT_ROWS}"
            
            result = self.sql_database.run(query)
            return str(result)
        except Exception as e:
            logger.error(f"Query execution error: {str(e)}")
            return f"Query execution failed: {str(e)}"
    
    def _get_db_type(self) -> str:
        """Get the database type from the connection URL.
        
        Returns:
            Database type string
        """
        if self.database_url.startswith("sqlite"):
            return "SQLite"
        elif self.database_url.startswith("postgresql"):
            return "PostgreSQL"
        elif self.database_url.startswith("mysql"):
            return "MySQL"
        else:
            return "Unknown"
    
    def close(self) -> None:
        """Close database connections."""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connection closed.")

# Global database manager instance
db_manager = DatabaseManager()

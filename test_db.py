#!/usr/bin/env python3
"""Test script to verify database connection and basic functionality."""

import sys
import logging
from config import config
from db import db_manager

def test_database_connection():
    """Test the database connection and basic operations."""
    print("🧪 Testing Database Connection...")
    
    # Setup logging
    config.setup_logging()
    
    # Test database connection
    if not db_manager.connect():
        print("❌ Failed to connect to database")
        return False
    
    print("✅ Database connection successful")
    
    # Test getting table names
    tables = db_manager.get_table_names()
    print(f"📋 Found {len(tables)} tables: {', '.join(tables)}")
    
    # Test getting table info
    if tables:
        table_info = db_manager.get_table_info()
        print(f"📊 Schema info length: {len(table_info)} characters")
        print("\n📋 Sample schema info:")
        print(table_info[:500] + "..." if len(table_info) > 500 else table_info)
    
    # Test a simple query
    print("\n🔍 Testing a simple query...")
    result = db_manager.test_query("SELECT COUNT(*) as customer_count FROM customers")
    print(f"Query result: {result}")
    
    # Test another query
    result = db_manager.test_query("SELECT product_name, price FROM products LIMIT 3")
    print(f"Products sample: {result}")
    
    # Close connection
    db_manager.close()
    print("✅ Database test completed successfully!")
    return True

if __name__ == "__main__":
    if test_database_connection():
        print("\n🎉 All database tests passed!")
        print("You can now run the full agent with: python main.py")
        print("(Make sure to set your OPENAI_API_KEY in the .env file)")
    else:
        print("\n❌ Database tests failed!")
        sys.exit(1)

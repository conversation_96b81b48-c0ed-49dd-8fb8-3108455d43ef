"""Command-line interface for the LangChain SQL Agent."""

import sys
from typing import Optional
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt
from rich.text import Text
from rich.markdown import Markdown
import click

from config import config
from db import db_manager
from agent import sql_agent

console = Console()

class SQLAgentCLI:
    """Command-line interface for the SQL Agent."""
    
    def __init__(self):
        """Initialize the CLI."""
        self.is_running = False
    
    def display_welcome(self) -> None:
        """Display welcome message and setup information."""
        welcome_text = """
# 🤖 LangChain SQL Agent

Welcome to the LangChain SQL Agent! This tool allows you to query your database using natural language.

## Features:
- 🗣️  Natural language database queries
- 🔍  Automatic SQL generation
- 📊  Rich formatted results
- 🛡️  Safe, read-only database access
- 📝  Query logging and debugging

## Commands:
- Type your question in natural language
- Type `help` for more information
- Type `info` to see database information
- Type `tables` to list available tables
- Type `quit` or `exit` to leave

---
        """
        
        console.print(Panel(
            Markdown(welcome_text),
            title="🚀 SQL Agent",
            border_style="blue"
        ))
    
    def display_database_info(self) -> None:
        """Display information about the connected database."""
        db_info = sql_agent.get_database_info()
        
        if not db_info.get("success", False):
            console.print(f"❌ Error getting database info: {db_info.get('error', 'Unknown error')}")
            return
        
        # Create a table for database information
        info_table = Table(title="📊 Database Information")
        info_table.add_column("Property", style="cyan")
        info_table.add_column("Value", style="green")
        
        info_table.add_row("Database Type", db_manager._get_db_type())
        info_table.add_row("Number of Tables", str(db_info.get("table_count", 0)))
        info_table.add_row("Available Tables", ", ".join(db_info.get("table_names", [])))
        
        console.print(info_table)
        
        # Display schema information if available
        schema_info = db_info.get("schema_info", "")
        if schema_info:
            console.print("\n📋 Schema Information:")
            console.print(Panel(schema_info, border_style="dim"))
    
    def display_tables(self) -> None:
        """Display available tables in the database."""
        table_names = db_manager.get_table_names()
        
        if not table_names:
            console.print("❌ No tables found or database not connected.")
            return
        
        tables_table = Table(title="📋 Available Tables")
        tables_table.add_column("Table Name", style="cyan")
        tables_table.add_column("Index", style="dim")
        
        for i, table_name in enumerate(table_names, 1):
            tables_table.add_row(table_name, str(i))
        
        console.print(tables_table)
    
    def display_help(self) -> None:
        """Display help information."""
        help_text = """
# 🆘 Help - How to Use the SQL Agent

## Natural Language Queries:
You can ask questions about your database in plain English. Here are some examples:

### Example Questions:
- "How many records are in the users table?"
- "What are the top 10 products by price?"
- "Show me all customers from California"
- "What's the average order value by month?"
- "List all employees hired in 2023"

### Special Commands:
- `help` - Show this help message
- `info` - Display database information and schema
- `tables` - List all available tables
- `quit` or `exit` - Exit the application

## Tips:
- Be specific about what data you want
- Mention table names if you know them
- Ask for limits (e.g., "top 10", "first 5") to avoid large results
- The agent will generate and execute SQL queries automatically

## Safety:
- All database access is read-only
- Query results are limited to {max_rows} rows
- Query timeout is set to {timeout} seconds
        """.format(max_rows=config.MAX_RESULT_ROWS, timeout=config.MAX_QUERY_TIMEOUT)
        
        console.print(Panel(
            Markdown(help_text),
            title="📚 Help",
            border_style="yellow"
        ))
    
    def process_query(self, question: str) -> None:
        """Process a user query and display results.
        
        Args:
            question: User's natural language question
        """
        with console.status("[bold green]Processing your query..."):
            result = sql_agent.query(question)
        
        if result["success"]:
            # Display the response
            console.print("\n✅ Query Result:")
            console.print(Panel(
                result["response"],
                title="🤖 Agent Response",
                border_style="green"
            ))
            
            # Display SQL query if available
            if result.get("sql_query") and config.ENABLE_QUERY_LOGGING:
                console.print("\n🔍 Generated SQL:")
                console.print(Panel(
                    result["sql_query"],
                    title="SQL Query",
                    border_style="blue"
                ))
        else:
            console.print(f"\n❌ Error: {result['error']}")
    
    def run(self) -> None:
        """Run the interactive CLI."""
        self.is_running = True
        
        # Display welcome message
        self.display_welcome()
        
        # Main interaction loop
        while self.is_running:
            try:
                # Get user input
                user_input = Prompt.ask(
                    "\n[bold cyan]Ask a question about your database[/bold cyan]",
                    default=""
                ).strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'q']:
                    self.is_running = False
                    console.print("👋 Goodbye!")
                    break
                elif user_input.lower() == 'help':
                    self.display_help()
                elif user_input.lower() == 'info':
                    self.display_database_info()
                elif user_input.lower() == 'tables':
                    self.display_tables()
                else:
                    # Process as a database query
                    self.process_query(user_input)
                    
            except KeyboardInterrupt:
                console.print("\n\n👋 Goodbye!")
                self.is_running = False
                break
            except Exception as e:
                console.print(f"\n❌ Unexpected error: {str(e)}")

@click.command()
@click.option('--database-url', help='Database connection URL')
@click.option('--verbose', is_flag=True, help='Enable verbose logging')
def main(database_url: Optional[str], verbose: bool) -> None:
    """LangChain SQL Agent - Query your database with natural language."""
    
    # Update config if database URL provided
    if database_url:
        config.DATABASE_URL = database_url
    
    # Update logging level if verbose
    if verbose:
        config.LOG_LEVEL = "DEBUG"
    
    # Setup logging
    config.setup_logging()
    
    # Validate configuration
    if not config.validate():
        console.print("❌ Configuration validation failed. Please check your .env file.")
        sys.exit(1)
    
    # Initialize database connection
    console.print("🔌 Connecting to database...")
    if not db_manager.connect():
        console.print("❌ Failed to connect to database. Please check your DATABASE_URL.")
        sys.exit(1)
    
    # Initialize SQL agent
    console.print("🤖 Initializing SQL Agent...")
    if not sql_agent.initialize():
        console.print("❌ Failed to initialize SQL Agent. Please check your OPENAI_API_KEY.")
        sys.exit(1)
    
    # Test the connection
    console.print("🧪 Testing agent connection...")
    if not sql_agent.test_connection():
        console.print("⚠️  Agent connection test failed, but continuing anyway...")
    
    # Start the CLI
    try:
        cli = SQLAgentCLI()
        cli.run()
    finally:
        # Cleanup
        db_manager.close()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
LangChain SQL Agent - Main Entry Point

This is the main entry point for the LangChain SQL Agent application.
It provides a command-line interface for querying databases using natural language.

Usage:
    python main.py                    # Use default configuration
    python main.py --verbose          # Enable verbose logging
    python main.py --database-url sqlite:///my_db.db  # Use custom database
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from interface import main

if __name__ == "__main__":
    main()

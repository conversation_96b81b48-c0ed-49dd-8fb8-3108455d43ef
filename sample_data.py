"""Sample database setup for testing the LangChain SQL Agent."""

import sqlite3
import logging
from datetime import datetime, timedelta
import random
from pathlib import Path

logger = logging.getLogger(__name__)

class SampleDatabaseCreator:
    """Creates a sample SQLite database with test data."""
    
    def __init__(self, db_path: str = "sample_database.db"):
        """Initialize the sample database creator.
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path
        self.conn = None
    
    def create_database(self) -> bool:
        """Create the sample database with tables and data.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Remove existing database file
            if Path(self.db_path).exists():
                Path(self.db_path).unlink()
            
            # Connect to database
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute("PRAGMA foreign_keys = ON")
            
            # Create tables
            self._create_tables()
            
            # Insert sample data
            self._insert_sample_data()
            
            # Commit changes
            self.conn.commit()
            
            logger.info(f"✅ Sample database created successfully: {self.db_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error creating sample database: {str(e)}")
            return False
        finally:
            if self.conn:
                self.conn.close()
    
    def _create_tables(self) -> None:
        """Create the database tables."""
        
        # Customers table
        self.conn.execute("""
            CREATE TABLE customers (
                customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                phone TEXT,
                city TEXT,
                state TEXT,
                country TEXT DEFAULT 'USA',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Products table
        self.conn.execute("""
            CREATE TABLE products (
                product_id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_name TEXT NOT NULL,
                category TEXT NOT NULL,
                price DECIMAL(10, 2) NOT NULL,
                stock_quantity INTEGER DEFAULT 0,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Orders table
        self.conn.execute("""
            CREATE TABLE orders (
                order_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                total_amount DECIMAL(10, 2) NOT NULL,
                status TEXT DEFAULT 'pending',
                shipping_address TEXT,
                FOREIGN KEY (customer_id) REFERENCES customers (customer_id)
            )
        """)
        
        # Order items table
        self.conn.execute("""
            CREATE TABLE order_items (
                item_id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price DECIMAL(10, 2) NOT NULL,
                FOREIGN KEY (order_id) REFERENCES orders (order_id),
                FOREIGN KEY (product_id) REFERENCES products (product_id)
            )
        """)
        
        # Employees table
        self.conn.execute("""
            CREATE TABLE employees (
                employee_id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                department TEXT NOT NULL,
                position TEXT NOT NULL,
                salary DECIMAL(10, 2),
                hire_date DATE NOT NULL,
                manager_id INTEGER,
                FOREIGN KEY (manager_id) REFERENCES employees (employee_id)
            )
        """)
    
    def _insert_sample_data(self) -> None:
        """Insert sample data into the tables."""
        
        # Sample customers
        customers = [
            ("John", "Doe", "<EMAIL>", "555-0101", "New York", "NY"),
            ("Jane", "Smith", "<EMAIL>", "555-0102", "Los Angeles", "CA"),
            ("Bob", "Johnson", "<EMAIL>", "555-0103", "Chicago", "IL"),
            ("Alice", "Brown", "<EMAIL>", "555-0104", "Houston", "TX"),
            ("Charlie", "Davis", "<EMAIL>", "555-0105", "Phoenix", "AZ"),
            ("Diana", "Wilson", "<EMAIL>", "555-0106", "Philadelphia", "PA"),
            ("Frank", "Miller", "<EMAIL>", "555-0107", "San Antonio", "TX"),
            ("Grace", "Taylor", "<EMAIL>", "555-0108", "San Diego", "CA"),
            ("Henry", "Anderson", "<EMAIL>", "555-0109", "Dallas", "TX"),
            ("Ivy", "Thomas", "<EMAIL>", "555-0110", "San Jose", "CA")
        ]
        
        for customer in customers:
            self.conn.execute(
                "INSERT INTO customers (first_name, last_name, email, phone, city, state) VALUES (?, ?, ?, ?, ?, ?)",
                customer
            )
        
        # Sample products
        products = [
            ("Laptop Pro", "Electronics", 1299.99, 50, "High-performance laptop"),
            ("Wireless Mouse", "Electronics", 29.99, 200, "Ergonomic wireless mouse"),
            ("Office Chair", "Furniture", 199.99, 30, "Comfortable office chair"),
            ("Standing Desk", "Furniture", 399.99, 15, "Adjustable standing desk"),
            ("Coffee Maker", "Appliances", 89.99, 75, "Programmable coffee maker"),
            ("Bluetooth Speaker", "Electronics", 79.99, 100, "Portable Bluetooth speaker"),
            ("Desk Lamp", "Furniture", 49.99, 60, "LED desk lamp"),
            ("Keyboard", "Electronics", 69.99, 80, "Mechanical keyboard"),
            ("Monitor", "Electronics", 299.99, 40, "24-inch 4K monitor"),
            ("Tablet", "Electronics", 499.99, 35, "10-inch tablet")
        ]
        
        for product in products:
            self.conn.execute(
                "INSERT INTO products (product_name, category, price, stock_quantity, description) VALUES (?, ?, ?, ?, ?)",
                product
            )
        
        # Sample employees
        employees = [
            ("Sarah", "Connor", "<EMAIL>", "Management", "CEO", 150000.00, "2020-01-15", None),
            ("Michael", "Scott", "<EMAIL>", "Sales", "Sales Manager", 75000.00, "2020-03-01", 1),
            ("Dwight", "Schrute", "<EMAIL>", "Sales", "Sales Representative", 55000.00, "2020-06-15", 2),
            ("Jim", "Halpert", "<EMAIL>", "Sales", "Sales Representative", 55000.00, "2020-08-01", 2),
            ("Pam", "Beesly", "<EMAIL>", "Administration", "Receptionist", 35000.00, "2021-01-10", 1),
            ("Angela", "Martin", "<EMAIL>", "Accounting", "Accountant", 50000.00, "2021-03-15", 1),
            ("Kevin", "Malone", "<EMAIL>", "Accounting", "Accountant", 45000.00, "2021-05-20", 6),
            ("Oscar", "Martinez", "<EMAIL>", "Accounting", "Senior Accountant", 60000.00, "2021-07-01", 6),
            ("Stanley", "Hudson", "<EMAIL>", "Sales", "Sales Representative", 52000.00, "2022-01-15", 2),
            ("Phyllis", "Vance", "<EMAIL>", "Sales", "Sales Representative", 53000.00, "2022-04-01", 2)
        ]
        
        for employee in employees:
            self.conn.execute(
                "INSERT INTO employees (first_name, last_name, email, department, position, salary, hire_date, manager_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                employee
            )
        
        # Generate sample orders and order items
        self._generate_orders()
    
    def _generate_orders(self) -> None:
        """Generate sample orders with random data."""
        
        # Get customer and product IDs
        customers = self.conn.execute("SELECT customer_id FROM customers").fetchall()
        products = self.conn.execute("SELECT product_id, price FROM products").fetchall()
        
        # Generate orders for the last 6 months
        start_date = datetime.now() - timedelta(days=180)
        
        for _ in range(50):  # Generate 50 orders
            # Random customer
            customer_id = random.choice(customers)[0]
            
            # Random order date
            order_date = start_date + timedelta(days=random.randint(0, 180))
            
            # Random status
            status = random.choice(['pending', 'processing', 'shipped', 'delivered', 'cancelled'])
            
            # Create order
            cursor = self.conn.execute(
                "INSERT INTO orders (customer_id, order_date, total_amount, status) VALUES (?, ?, ?, ?)",
                (customer_id, order_date, 0.0, status)  # Will update total_amount later
            )
            order_id = cursor.lastrowid
            
            # Add 1-4 items to each order
            total_amount = 0.0
            num_items = random.randint(1, 4)
            
            for _ in range(num_items):
                product_id, price = random.choice(products)
                quantity = random.randint(1, 3)
                unit_price = float(price)
                
                self.conn.execute(
                    "INSERT INTO order_items (order_id, product_id, quantity, unit_price) VALUES (?, ?, ?, ?)",
                    (order_id, product_id, quantity, unit_price)
                )
                
                total_amount += unit_price * quantity
            
            # Update order total
            self.conn.execute(
                "UPDATE orders SET total_amount = ? WHERE order_id = ?",
                (total_amount, order_id)
            )

def create_sample_database(db_path: str = "sample_database.db") -> bool:
    """Create a sample database for testing.
    
    Args:
        db_path: Path to the database file
        
    Returns:
        bool: True if successful, False otherwise
    """
    creator = SampleDatabaseCreator(db_path)
    return creator.create_database()

if __name__ == "__main__":
    # Create sample database when run directly
    import logging
    logging.basicConfig(level=logging.INFO)
    
    if create_sample_database():
        print("✅ Sample database created successfully!")
        print("You can now run the SQL Agent with: python main.py")
    else:
        print("❌ Failed to create sample database.")
        sys.exit(1)
